{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-27T07:00:04.514Z", "updatedAt": "2025-07-27T07:00:04.871Z", "resourceCount": 31}, "resources": [{"id": "dev-team-coordinator", "source": "project", "protocol": "role", "name": "Dev Team Coordinator 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/dev-team-coordinator/dev-team-coordinator.role.md", "metadata": {"createdAt": "2025-07-27T07:00:04.518Z", "updatedAt": "2025-07-27T07:00:04.518Z", "scannedAt": "2025-07-27T07:00:04.518Z", "path": "domain/dev-team-coordinator/dev-team-coordinator.role.md"}}, {"id": "bug-analysis-workflow", "source": "project", "protocol": "execution", "name": "Bug Analysis Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-developer/execution/bug-analysis-workflow.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.637Z", "updatedAt": "2025-07-27T07:00:04.637Z", "scannedAt": "2025-07-27T07:00:04.637Z", "path": "domain/fullstack-developer/execution/bug-analysis-workflow.execution.md"}}, {"id": "solution-evaluation", "source": "project", "protocol": "execution", "name": "Solution Evaluation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-developer/execution/solution-evaluation.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.693Z", "updatedAt": "2025-07-27T07:00:04.693Z", "scannedAt": "2025-07-27T07:00:04.693Z", "path": "domain/fullstack-developer/execution/solution-evaluation.execution.md"}}, {"id": "team-meeting-process", "source": "project", "protocol": "execution", "name": "Team Meeting Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/dev-team-coordinator/execution/team-meeting-process.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.577Z", "updatedAt": "2025-07-27T07:00:04.577Z", "scannedAt": "2025-07-27T07:00:04.577Z", "path": "domain/dev-team-coordinator/execution/team-meeting-process.execution.md"}}, {"id": "fullstack-development", "source": "project", "protocol": "knowledge", "name": "Fullstack Development 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/dev-team-coordinator/knowledge/fullstack-development.knowledge.md", "metadata": {"createdAt": "2025-07-27T07:00:04.587Z", "updatedAt": "2025-07-27T07:00:04.587Z", "scannedAt": "2025-07-27T07:00:04.587Z", "path": "domain/dev-team-coordinator/knowledge/fullstack-development.knowledge.md"}}, {"id": "backend-expert-thinking", "source": "project", "protocol": "thought", "name": "Backend Expert Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/dev-team-coordinator/thought/backend-expert-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.595Z", "updatedAt": "2025-07-27T07:00:04.595Z", "scannedAt": "2025-07-27T07:00:04.595Z", "path": "domain/dev-team-coordinator/thought/backend-expert-thinking.thought.md"}}, {"id": "frontend-expert-thinking", "source": "project", "protocol": "thought", "name": "Frontend Expert Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/dev-team-coordinator/thought/frontend-expert-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.608Z", "updatedAt": "2025-07-27T07:00:04.608Z", "scannedAt": "2025-07-27T07:00:04.608Z", "path": "domain/dev-team-coordinator/thought/frontend-expert-thinking.thought.md"}}, {"id": "project-manager-thinking", "source": "project", "protocol": "thought", "name": "Project Manager Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/dev-team-coordinator/thought/project-manager-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.625Z", "updatedAt": "2025-07-27T07:00:04.625Z", "scannedAt": "2025-07-27T07:00:04.625Z", "path": "domain/dev-team-coordinator/thought/project-manager-thinking.thought.md"}}, {"id": "team-collaboration", "source": "project", "protocol": "thought", "name": "Team Collaboration 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/dev-team-coordinator/thought/team-collaboration.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.627Z", "updatedAt": "2025-07-27T07:00:04.627Z", "scannedAt": "2025-07-27T07:00:04.627Z", "path": "domain/dev-team-coordinator/thought/team-collaboration.thought.md"}}, {"id": "business-analysis-workflow", "source": "project", "protocol": "execution", "name": "Business Analysis Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-developer/execution/business-analysis-workflow.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.648Z", "updatedAt": "2025-07-27T07:00:04.648Z", "scannedAt": "2025-07-27T07:00:04.648Z", "path": "domain/fullstack-developer/execution/business-analysis-workflow.execution.md"}}, {"id": "error-report-analysis-workflow", "source": "project", "protocol": "execution", "name": "Error Report Analysis Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-developer/execution/error-report-analysis-workflow.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.655Z", "updatedAt": "2025-07-27T07:00:04.655Z", "scannedAt": "2025-07-27T07:00:04.655Z", "path": "domain/fullstack-developer/execution/error-report-analysis-workflow.execution.md"}}, {"id": "frontend-backend-integration", "source": "project", "protocol": "execution", "name": "Frontend Backend Integration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-developer/execution/frontend-backend-integration.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.660Z", "updatedAt": "2025-07-27T07:00:04.660Z", "scannedAt": "2025-07-27T07:00:04.660Z", "path": "domain/fullstack-developer/execution/frontend-backend-integration.execution.md"}}, {"id": "minimal-change-principle", "source": "project", "protocol": "execution", "name": "Minimal Change Principle 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-developer/execution/minimal-change-principle.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.662Z", "updatedAt": "2025-07-27T07:00:04.662Z", "scannedAt": "2025-07-27T07:00:04.662Z", "path": "domain/fullstack-developer/execution/minimal-change-principle.execution.md"}}, {"id": "pre-development-analysis", "source": "project", "protocol": "execution", "name": "Pre Development Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-developer/execution/pre-development-analysis.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.669Z", "updatedAt": "2025-07-27T07:00:04.669Z", "scannedAt": "2025-07-27T07:00:04.669Z", "path": "domain/fullstack-developer/execution/pre-development-analysis.execution.md"}}, {"id": "project-management-workflow", "source": "project", "protocol": "execution", "name": "Project Management Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-developer/execution/project-management-workflow.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.683Z", "updatedAt": "2025-07-27T07:00:04.683Z", "scannedAt": "2025-07-27T07:00:04.683Z", "path": "domain/fullstack-developer/execution/project-management-workflow.execution.md"}}, {"id": "rapid-collaboration-workflow", "source": "project", "protocol": "execution", "name": "Rapid Collaboration Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-developer/execution/rapid-collaboration-workflow.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.691Z", "updatedAt": "2025-07-27T07:00:04.691Z", "scannedAt": "2025-07-27T07:00:04.691Z", "path": "domain/fullstack-developer/execution/rapid-collaboration-workflow.execution.md"}}, {"id": "fullstack-developer", "source": "project", "protocol": "role", "name": "Fullstack Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/fullstack-developer/fullstack-developer.role.md", "metadata": {"createdAt": "2025-07-27T07:00:04.705Z", "updatedAt": "2025-07-27T07:00:04.705Z", "scannedAt": "2025-07-27T07:00:04.705Z", "path": "domain/fullstack-developer/fullstack-developer.role.md"}}, {"id": "fullstack-rickpan", "source": "project", "protocol": "knowledge", "name": "Fullstack Rickpan 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/fullstack-developer/knowledge/fullstack-rickpan.knowledge.md", "metadata": {"createdAt": "2025-07-27T07:00:04.722Z", "updatedAt": "2025-07-27T07:00:04.722Z", "scannedAt": "2025-07-27T07:00:04.722Z", "path": "domain/fullstack-developer/knowledge/fullstack-rickpan.knowledge.md"}}, {"id": "project-business-management", "source": "project", "protocol": "knowledge", "name": "Project Business Management 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/fullstack-developer/knowledge/project-business-management.knowledge.md", "metadata": {"createdAt": "2025-07-27T07:00:04.727Z", "updatedAt": "2025-07-27T07:00:04.727Z", "scannedAt": "2025-07-27T07:00:04.727Z", "path": "domain/fullstack-developer/knowledge/project-business-management.knowledge.md"}}, {"id": "api-memory", "source": "project", "protocol": "thought", "name": "Api Memory 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-developer/thought/api-memory.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.742Z", "updatedAt": "2025-07-27T07:00:04.742Z", "scannedAt": "2025-07-27T07:00:04.742Z", "path": "domain/fullstack-developer/thought/api-memory.thought.md"}}, {"id": "business-understanding-thinking", "source": "project", "protocol": "thought", "name": "Business Understanding Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-developer/thought/business-understanding-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.753Z", "updatedAt": "2025-07-27T07:00:04.753Z", "scannedAt": "2025-07-27T07:00:04.753Z", "path": "domain/fullstack-developer/thought/business-understanding-thinking.thought.md"}}, {"id": "deep-thinking", "source": "project", "protocol": "thought", "name": "Deep Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-developer/thought/deep-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.762Z", "updatedAt": "2025-07-27T07:00:04.762Z", "scannedAt": "2025-07-27T07:00:04.762Z", "path": "domain/fullstack-developer/thought/deep-thinking.thought.md"}}, {"id": "fullstack-coordination", "source": "project", "protocol": "thought", "name": "Fullstack Coordination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-developer/thought/fullstack-coordination.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.767Z", "updatedAt": "2025-07-27T07:00:04.767Z", "scannedAt": "2025-07-27T07:00:04.767Z", "path": "domain/fullstack-developer/thought/fullstack-coordination.thought.md"}}, {"id": "proactive-error-prevention", "source": "project", "protocol": "thought", "name": "Proactive Error Prevention 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-developer/thought/proactive-error-prevention.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.783Z", "updatedAt": "2025-07-27T07:00:04.783Z", "scannedAt": "2025-07-27T07:00:04.783Z", "path": "domain/fullstack-developer/thought/proactive-error-prevention.thought.md"}}, {"id": "project-management-thinking", "source": "project", "protocol": "thought", "name": "Project Management Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-developer/thought/project-management-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.801Z", "updatedAt": "2025-07-27T07:00:04.801Z", "scannedAt": "2025-07-27T07:00:04.801Z", "path": "domain/fullstack-developer/thought/project-management-thinking.thought.md"}}, {"id": "quick-decision-framework", "source": "project", "protocol": "thought", "name": "Quick Decision Framework 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-developer/thought/quick-decision-framework.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.813Z", "updatedAt": "2025-07-27T07:00:04.813Z", "scannedAt": "2025-07-27T07:00:04.813Z", "path": "domain/fullstack-developer/thought/quick-decision-framework.thought.md"}}, {"id": "rickpan-domain", "source": "project", "protocol": "thought", "name": "Rickpan Domain 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-developer/thought/rickpan-domain.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.829Z", "updatedAt": "2025-07-27T07:00:04.829Z", "scannedAt": "2025-07-27T07:00:04.829Z", "path": "domain/fullstack-developer/thought/rickpan-domain.thought.md"}}, {"id": "second-thinking", "source": "project", "protocol": "thought", "name": "Second Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-developer/thought/second-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.831Z", "updatedAt": "2025-07-27T07:00:04.831Z", "scannedAt": "2025-07-27T07:00:04.831Z", "path": "domain/fullstack-developer/thought/second-thinking.thought.md"}}, {"id": "riper5-macro-micro-cycles", "source": "project", "protocol": "execution", "name": "Riper5 Macro Micro Cycles 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/riper5-fullstack-expert/execution/riper5-macro-micro-cycles.execution.md", "metadata": {"createdAt": "2025-07-27T07:00:04.855Z", "updatedAt": "2025-07-27T07:00:04.855Z", "scannedAt": "2025-07-27T07:00:04.855Z", "path": "role/riper5-fullstack-expert/execution/riper5-macro-micro-cycles.execution.md"}}, {"id": "riper5-fullstack-expert", "source": "project", "protocol": "role", "name": "Riper5 Fullstack Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/riper5-fullstack-expert/riper5-fullstack-expert.role.md", "metadata": {"createdAt": "2025-07-27T07:00:04.867Z", "updatedAt": "2025-07-27T07:00:04.867Z", "scannedAt": "2025-07-27T07:00:04.867Z", "path": "role/riper5-fullstack-expert/riper5-fullstack-expert.role.md"}}, {"id": "riper5-dual-mode-thinking", "source": "project", "protocol": "thought", "name": "Riper5 Dual Mode Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/riper5-fullstack-expert/thought/riper5-dual-mode-thinking.thought.md", "metadata": {"createdAt": "2025-07-27T07:00:04.869Z", "updatedAt": "2025-07-27T07:00:04.869Z", "scannedAt": "2025-07-27T07:00:04.869Z", "path": "role/riper5-fullstack-expert/thought/riper5-dual-mode-thinking.thought.md"}}], "stats": {"totalResources": 31, "byProtocol": {"role": 3, "execution": 11, "knowledge": 3, "thought": 14}, "bySource": {"project": 31}}}